@echo off
echo ================================
echo AI聊天助手 - 测试程序
echo ================================
echo.

echo 正在编译Java文件...
javac -cp src src/Main.java src/LoginFrame.java src/EmailSender.java

if %errorlevel% neq 0 (
    echo 编译失败！请检查Java环境。
    pause
    exit /b 1
)

echo 编译成功！
echo.
echo 正在启动程序...
echo.
echo 测试说明：
echo 1. 输入任意邮箱地址
echo 2. 点击"发送验证码"
echo 3. 在弹出的对话框中查看验证码
echo 4. 输入验证码并点击"登录"
echo 5. 应该会跳转到聊天界面
echo.

java -cp src Main

echo.
echo 程序已退出。
pause
