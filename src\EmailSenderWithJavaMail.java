/*
 * 真实的JavaMail邮件发送器
 * 使用前需要添加JavaMail依赖
 * 
 * Maven依赖：
 * <dependency>
 *     <groupId>com.sun.mail</groupId>
 *     <artifactId>javax.mail</artifactId>
 *     <version>1.6.2</version>
 * </dependency>
 * 
 * 或者下载jar文件：
 * - javax.mail-1.6.2.jar
 * - activation-1.1.1.jar
 */

import javax.mail.*;
import javax.mail.internet.*;
import java.util.Properties;

public class EmailSenderWithJavaMail {
    // 邮件配置信息
    private static final String SMTP_HOST = "smtp.qq.com";
    private static final String SMTP_PORT = "587";
    private static final String SENDER_EMAIL = "<EMAIL>";
    private static String SENDER_PASSWORD = ""; // 需要填入QQ邮箱授权码
    
    /**
     * 发送验证码邮件
     * @param recipientEmail 收件人邮箱
     * @param verificationCode 验证码
     * @return 发送是否成功
     */
    public static boolean sendVerificationCode(String recipientEmail, String verificationCode) {
        // 检查授权码是否已配置
        if (SENDER_PASSWORD.isEmpty()) {
            System.err.println("邮箱授权码未配置，请先调用setSenderPassword()设置授权码");
            return false;
        }
        
        try {
            // 设置邮件服务器属性
            Properties props = new Properties();
            props.put("mail.smtp.host", SMTP_HOST);
            props.put("mail.smtp.port", SMTP_PORT);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
            props.put("mail.debug", "false"); // 设置为true可以看到详细的发送日志
            
            // 创建认证器
            Authenticator authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(SENDER_EMAIL, SENDER_PASSWORD);
                }
            };
            
            // 创建会话
            Session session = Session.getInstance(props, authenticator);
            
            // 创建邮件消息
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(SENDER_EMAIL, "AI聊天助手"));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipientEmail));
            message.setSubject("AI聊天助手 - 验证码");
            
            // 设置邮件内容
            String emailContent = String.format(
                "您好！\n\n" +
                "您正在登录AI聊天助手，您的验证码是：%s\n\n" +
                "验证码有效期为5分钟，请及时使用。\n" +
                "如果这不是您的操作，请忽略此邮件。\n\n" +
                "祝您使用愉快！\n" +
                "AI聊天助手团队",
                verificationCode
            );
            message.setText(emailContent);
            
            // 发送邮件
            Transport.send(message);
            System.out.println("验证码邮件发送成功到: " + recipientEmail);
            return true;
            
        } catch (MessagingException e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            System.err.println("发送邮件时出现未知错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 设置邮箱授权码
     * @param password QQ邮箱的授权码（不是QQ密码）
     */
    public static void setSenderPassword(String password) {
        SENDER_PASSWORD = password;
        System.out.println("邮箱授权码已设置");
    }
    
    /**
     * 测试邮件发送功能
     * @param testEmail 测试邮箱
     */
    public static void testEmailSending(String testEmail) {
        if (SENDER_PASSWORD.isEmpty()) {
            System.out.println("请先设置邮箱授权码：EmailSenderWithJavaMail.setSenderPassword(\"你的授权码\");");
            return;
        }
        
        String testCode = "1234";
        System.out.println("正在测试邮件发送功能...");
        boolean success = sendVerificationCode(testEmail, testCode);
        
        if (success) {
            System.out.println("邮件发送测试成功！");
        } else {
            System.out.println("邮件发送测试失败，请检查配置。");
        }
    }
}
