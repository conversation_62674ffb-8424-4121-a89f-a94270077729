import java.io.*;
import java.net.*;
import java.util.Base64;
import javax.net.ssl.*;

public class EmailSender {
    private static final String SMTP_HOST = "smtp.qq.com";
    private static final int SMTP_PORT = 587;
    private static final String SENDER_EMAIL = "<EMAIL>";
    private static final String SENDER_PASSWORD = "xwewerhxkbecbgbe";

    /**
     * 发送验证码邮件
     * @param recipientEmail 收件人邮箱
     * @param verificationCode 验证码
     * @return 发送是否成功
     */
    public static boolean sendVerificationCode(String recipientEmail, String verificationCode) {
        try {
            System.out.println("正在连接SMTP服务器发送邮件...");

            // 创建Socket连接
            Socket socket = new Socket(SMTP_HOST, SMTP_PORT);
            BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);

            // 读取服务器欢迎信息
            String response = readResponse(reader);
            System.out.println("服务器响应: " + response);

            // 发送EHLO命令
            writer.println("EHLO localhost");
            response = readResponse(reader);
            System.out.println("EHLO响应: " + response);

            // 发送STARTTLS命令启用加密
            writer.println("STARTTLS");
            response = readResponse(reader);
            System.out.println("STARTTLS响应: " + response);

            if (!response.startsWith("220")) {
                throw new Exception("STARTTLS命令失败");
            }

            // 升级到SSL连接
            SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
            SSLSocket sslSocket = (SSLSocket) factory.createSocket(socket, SMTP_HOST, SMTP_PORT, true);

            reader = new BufferedReader(new InputStreamReader(sslSocket.getInputStream()));
            writer = new PrintWriter(sslSocket.getOutputStream(), true);

            // 重新发送EHLO
            writer.println("EHLO localhost");
            response = readResponse(reader);
            System.out.println("SSL EHLO响应: " + response);

            // 认证
            writer.println("AUTH LOGIN");
            response = readResponse(reader);
            System.out.println("AUTH响应: " + response);

            // 发送用户名（Base64编码）
            String encodedUsername = Base64.getEncoder().encodeToString(SENDER_EMAIL.getBytes());
            writer.println(encodedUsername);
            response = readResponse(reader);
            System.out.println("用户名响应: " + response);

            // 发送密码（Base64编码）
            String encodedPassword = Base64.getEncoder().encodeToString(SENDER_PASSWORD.getBytes());
            writer.println(encodedPassword);
            response = readResponse(reader);
            System.out.println("密码响应: " + response);

            if (!response.startsWith("235")) {
                throw new Exception("认证失败，请检查邮箱和授权码");
            }

            // 发送邮件
            sendEmail(writer, reader, recipientEmail, verificationCode);

            // 关闭连接
            writer.println("QUIT");
            sslSocket.close();
            socket.close();

            System.out.println("验证码邮件发送成功到: " + recipientEmail);
            return true;

        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
            System.out.println("临时验证码（用于测试）: " + verificationCode);
            return false;
        }
    }

    private static String readResponse(BufferedReader reader) throws IOException {
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
            if (line.length() >= 4 && line.charAt(3) == ' ') {
                break; // 单行响应或多行响应的最后一行
            }
            response.append("\n");
        }
        return response.toString();
    }

    private static void sendEmail(PrintWriter writer, BufferedReader reader,
                                 String recipientEmail, String verificationCode) throws IOException {
        // MAIL FROM
        writer.println("MAIL FROM:<" + SENDER_EMAIL + ">");
        String response = readResponse(reader);
        System.out.println("MAIL FROM响应: " + response);

        // RCPT TO
        writer.println("RCPT TO:<" + recipientEmail + ">");
        response = readResponse(reader);
        System.out.println("RCPT TO响应: " + response);

        // DATA
        writer.println("DATA");
        response = readResponse(reader);
        System.out.println("DATA响应: " + response);

        // 邮件内容
        writer.println("From: AI聊天助手 <" + SENDER_EMAIL + ">");
        writer.println("To: " + recipientEmail);
        writer.println("Subject: =?UTF-8?B?" + Base64.getEncoder().encodeToString("AI聊天助手验证码".getBytes("UTF-8")) + "?=");
        writer.println("Content-Type: text/plain; charset=UTF-8");
        writer.println("Content-Transfer-Encoding: base64");
        writer.println();

        String emailContent = String.format(
            "您好！\n\n" +
            "您正在登录AI聊天助手，您的验证码是：%s\n\n" +
            "验证码有效期为5分钟，请及时使用。\n" +
            "如果这不是您的操作，请忽略此邮件。\n\n" +
            "祝您使用愉快！\n" +
            "AI聊天助手团队",
            verificationCode
        );

        String encodedContent = Base64.getEncoder().encodeToString(emailContent.getBytes("UTF-8"));
        writer.println(encodedContent);
        writer.println(".");

        response = readResponse(reader);
        System.out.println("邮件发送响应: " + response);
    }
}
