// 注意：此类需要JavaMail依赖才能正常工作
// 当前为简化版本，仅用于演示
public class EmailSender {
    // 邮件配置信息
    private static final String SMTP_HOST = "smtp.qq.com";
    private static final String SMTP_PORT = "587";
    private static final String SENDER_EMAIL = "<EMAIL>";
    private static String SENDER_PASSWORD = "xwewerhxkbecbgbe"; // 需要填入授权码

    /**
     * 发送验证码邮件（简化版本）
     * 注意：此方法需要JavaMail依赖才能真正发送邮件
     * @param recipientEmail 收件人邮箱
     * @param verificationCode 验证码
     * @return 发送是否成功
     */
    public static boolean sendVerificationCode(String recipientEmail, String verificationCode) {
        // 检查授权码是否已配置
        if (SENDER_PASSWORD.isEmpty()) {
            System.err.println("邮箱授权码未配置，请先调用setSenderPassword()设置授权码");
            System.out.println("模拟发送验证码到: " + recipientEmail + ", 验证码: " + verificationCode);
            return false;
        }

        try {
            // 模拟邮件发送过程
            System.out.println("正在发送验证码邮件...");
            System.out.println("收件人: " + recipientEmail);
            System.out.println("验证码: " + verificationCode);
            System.out.println("发送者: " + SENDER_EMAIL);

            // 模拟网络延迟
            Thread.sleep(1000);

            // 在真实环境中，这里应该使用JavaMail发送邮件
            // 当前返回false表示需要配置JavaMail
            System.out.println("注意：当前为模拟发送，需要添加JavaMail依赖才能真正发送邮件");
            return false;

        } catch (InterruptedException e) {
            System.err.println("邮件发送被中断: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置邮箱授权码（用于运行时配置）
     * @param password 邮箱授权码
     */
    public static void setSenderPassword(String password) {
        // 注意：这种方式不安全，仅用于演示
        // 实际项目中应该使用配置文件或环境变量
        try {
            java.lang.reflect.Field field = EmailSender.class.getDeclaredField("SENDER_PASSWORD");
            field.setAccessible(true);
            field.set(null, password);
        } catch (Exception e) {
            System.err.println("设置密码失败: " + e.getMessage());
        }
    }
}
