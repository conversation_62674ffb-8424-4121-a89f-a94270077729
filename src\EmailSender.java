public class EmailSender {

    /**
     * 发送验证码邮件（简化版本）
     * @param recipientEmail 收件人邮箱
     * @param verificationCode 验证码
     * @return 发送是否成功
     */
    public static boolean sendVerificationCode(String recipientEmail, String verificationCode) {
        try {
            System.out.println("正在发送验证码邮件...");
            System.out.println("收件人: " + recipientEmail);
            System.out.println("验证码: " + verificationCode);
            System.out.println("发送者: <EMAIL>");

            // 模拟发送延迟
            Thread.sleep(500);

            System.out.println("验证码邮件发送成功！");
            return true;

        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            System.out.println("测试用验证码: " + verificationCode);
            return false;
        }
    }

    /**
     * 设置邮箱授权码（用于运行时配置）
     * @param password 邮箱授权码
     */
    public static void setSenderPassword(String password) {
        // 注意：这种方式不安全，仅用于演示
        // 实际项目中应该使用配置文件或环境变量
        try {
            java.lang.reflect.Field field = EmailSender.class.getDeclaredField("SENDER_PASSWORD");
            field.setAccessible(true);
            field.set(null, password);
        } catch (Exception e) {
            System.err.println("设置密码失败: " + e.getMessage());
        }
    }
}
