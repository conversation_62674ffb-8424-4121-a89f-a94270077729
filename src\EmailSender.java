public class EmailSender {

    public static boolean sendVerificationCode(String recipientEmail, String verificationCode) {
        try {
            System.out.println("发送验证码到: " + recipientEmail);
            System.out.println("验证码: " + verificationCode);

            // 简单模拟发送
            Thread.sleep(1000);

            System.out.println("邮件发送成功！");
            return true;

        } catch (Exception e) {
            System.out.println("验证码: " + verificationCode);
            return false;
        }
    }
}
