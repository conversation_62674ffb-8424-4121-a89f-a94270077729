# JavaMail邮件发送配置说明

## 问题解决状态

✅ **登录跳转问题已修复**：登录成功后现在会正确跳转到聊天界面

⚠️ **邮件发送功能**：当前为模拟发送，需要配置JavaMail依赖才能真实发送邮件

## 快速测试

1. 运行程序：`java -cp src Main`
2. 输入任意邮箱地址
3. 点击"发送验证码"（会显示模拟发送的验证码）
4. 输入显示的验证码
5. 点击"登录"即可进入聊天界面

## 配置真实邮件发送

### 方法一：使用Maven项目（推荐）

1. **创建Maven项目结构**：
```
kechengsheji/
├── pom.xml
└── src/
    └── main/
        └── java/
            ├── Main.java
            ├── LoginFrame.java
            ├── EmailSender.java
            └── EmailSenderWithJavaMail.java
```

2. **创建pom.xml文件**：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>ai-chat</artifactId>
    <version>1.0.0</version>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
    </dependencies>
</project>
```

3. **编译和运行**：
```bash
mvn compile
mvn exec:java -Dexec.mainClass="Main"
```

### 方法二：手动下载JAR文件

1. **下载所需的JAR文件**：
   - [javax.mail-1.6.2.jar](https://repo1.maven.org/maven2/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar)
   - [activation-1.1.1.jar](https://repo1.maven.org/maven2/javax/activation/activation/1.1.1/activation-1.1.1.jar)

2. **将JAR文件放在lib目录**：
```
kechengsheji/
├── lib/
│   ├── javax.mail-1.6.2.jar
│   └── activation-1.1.1.jar
└── src/
    └── ...
```

3. **编译和运行**：
```bash
# 编译
javac -cp "src;lib/*" src/*.java

# 运行
java -cp "src;lib/*" Main
```

### 配置QQ邮箱授权码

1. **登录QQ邮箱** → 设置 → 账户
2. **开启SMTP服务**，获取授权码（不是QQ密码）
3. **在代码中设置授权码**：

```java
// 在Main.java的main方法开始处添加：
EmailSenderWithJavaMail.setSenderPassword("你的QQ邮箱授权码");
```

### 使用真实邮件发送

配置完成后，修改LoginFrame.java中的邮件发送调用：

```java
// 将这行：
boolean success = EmailSender.sendVerificationCode(email, generatedCode);

// 改为：
boolean success = EmailSenderWithJavaMail.sendVerificationCode(email, generatedCode);
```

## 测试邮件发送

```java
public static void main(String[] args) {
    // 设置邮箱授权码
    EmailSenderWithJavaMail.setSenderPassword("你的授权码");
    
    // 测试邮件发送
    EmailSenderWithJavaMail.testEmailSending("<EMAIL>");
    
    // 启动程序
    SwingUtilities.invokeLater(() -> {
        LoginFrame loginFrame = new LoginFrame();
        loginFrame.setVisible(true);
    });
}
```

## 常见问题

1. **邮件发送失败**：
   - 检查网络连接
   - 确认QQ邮箱已开启SMTP服务
   - 确认授权码正确（不是QQ密码）

2. **编译错误**：
   - 确保JavaMail依赖已正确添加
   - 检查Java版本兼容性

3. **防火墙问题**：
   - 确保防火墙允许Java程序访问网络
   - 某些企业网络可能阻止SMTP连接

## 当前功能状态

- ✅ 登录界面
- ✅ 验证码生成
- ✅ 登录验证
- ✅ 跳转到聊天界面
- ✅ AI聊天功能
- ⚠️ 邮件发送（需要配置JavaMail）
