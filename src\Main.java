import javax.swing.*;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;
import java.awt.*;
import java.awt.event.ActionEvent;
//import java.awt.event.ActionListener;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class Main {

    //API配置信息
    private  static final String Deepseek_API = "***********************************";
    private  static  final String API_URL = "https://api.deepseek.com/chat/completions";
    private static JTextArea chatArea;

    public static void main(String[] args) {
        // 启动登录界面
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                LoginFrame loginFrame = new LoginFrame();
                loginFrame.setVisible(true);
            }
        });
    }
    
    // 创建并显示主聊天界面
    public static void createAndShowMainFrame() {
        //创建主窗口
        JFrame frame = new JFrame("AI聊天");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800,600);
        frame.setLayout(new BorderLayout());

        //顶部工具栏
        JToolBar toptool = new JToolBar();
        frame.add(toptool, BorderLayout.NORTH);
        toptool.add(createToolBar());

        //聊天记录区域
        JScrollPane chatScrollPane = createChatArea();
        frame.add(chatScrollPane, BorderLayout.CENTER);

        //底部输入区域
        JPanel inputPanel = createInputPanel(frame);
        frame.add(inputPanel, BorderLayout.SOUTH);

        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }

    //主窗口
    private static JToolBar createToolBar(){
        JToolBar toolBar = new JToolBar();
        toolBar.setFloatable(false);   //禁止拖动
        toolBar.add(new JLabel("AI聊天助手 v1.0"));
        toolBar.addSeparator();
        toolBar.add(new JButton("历史记录"));
        toolBar.add(new JButton("设置"));
        return toolBar;
    }


    private static JScrollPane createChatArea() {
        chatArea = new JTextArea();
        chatArea.setEditable(false);
        chatArea.setLineWrap(true);
        chatArea.setWrapStyleWord(true);
        chatArea.setMargin(new Insets(10,10,10,10));
        chatArea.setFont(new Font("微软雅黑",Font.PLAIN,14));

        JScrollPane scrollPane = new JScrollPane(chatArea);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        return scrollPane;
    }

    private static JPanel createInputPanel(JFrame frame){
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(5,5,5,5));

        //输入框
        JTextArea inputArea = new JTextArea(3, 20);
        inputArea.setLineWrap(true);
        inputArea.setWrapStyleWord(true);
        inputArea.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        JScrollPane inputScroll = new JScrollPane(inputArea);

        JButton sendButton = new JButton("发送");
        sendButton.setPreferredSize(new Dimension(80, 50));

        sendButton.addActionListener(e -> {
            String message = inputArea.getText().trim();
            if (!message.isEmpty()) {
                addMessage("用户", message, true); // 用户消息
                inputArea.setText("");
                //异步调用
                new Thread(() ->{
                    try{
                        String response = callDeepSeekAPI(message);
                        SwingUtilities.invokeLater(()-> addMessage("Deepseek",response,false));
                    }catch (IOException ex){
                        SwingUtilities.invokeLater(()-> JOptionPane.showMessageDialog(frame,"API调用失败" + ex.getMessage(),"错误",JOptionPane.ERROR_MESSAGE));
                    }

                }).start();
            }

        });
        // 布局组合
        panel.add(inputScroll, BorderLayout.CENTER);
        panel.add(sendButton, BorderLayout.EAST);

        inputArea.getInputMap().put(KeyStroke.getKeyStroke("ENTER"), "sendAction");
        inputArea.getActionMap().put("sendAction", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                sendButton.doClick();
                inputArea.setText("");
            }
        });

        return panel;
    }
    private static void addMessage(String sender, String message, boolean isUser) {
        String formatted = String.format("[%s] %s:\n%s\n\n",
                new java.util.Date().toString().split(" ")[3], // 时间
                sender,
                message);

        // 颜色标记
        SimpleAttributeSet attributes = new SimpleAttributeSet();
        StyleConstants.setForeground(attributes, isUser ? Color.BLUE : new Color(46, 125, 50));
        StyleConstants.setBold(attributes, true);

        try {
            chatArea.getDocument().insertString(
                    chatArea.getDocument().getLength(),
                    formatted,
                    attributes);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    //API调用
    private static String callDeepSeekAPI(String userMessage) throws IOException{


        //构建请求体
        String requestBody = String.format(
                "{\"model\":\"deepseek-chat\",\"messages\":[{\"role\":\"user\",\"content\":\"%s\"}],\"temperature\":1.3}",
                userMessage.replace("\"", "\\\"")
                        .replace("\\", "\\\\") // 额外转义处理
        );

        HttpURLConnection connection = null;
        try{
            URL url = new URL(API_URL);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type","application/json");
            connection.setRequestProperty("Authorization", "Bearer " + Deepseek_API);
            connection.setDoOutput(true);

            try (OutputStream os = connection.getOutputStream()){
                byte [] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input,0,input.length);
            }

            //处理相应
            int status = connection.getResponseCode();
            if(status == HttpURLConnection.HTTP_OK){
                try(BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(),StandardCharsets.UTF_8))){
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while((responseLine = br.readLine())!=null){
                        response.append(responseLine.trim());
                    }
                    return parseAPIResponse(response.toString());
                }
            } else {
                throw new IOException("HTTP ERROR:" + status + "-" + connection.getRequestMethod());
            }
        }finally {
            if (connection != null) {
                connection.disconnect();
            }


        }
    }

    private static String parseAPIResponse(String jsonResponse){
        try {
            if (jsonResponse.contains("\"content\":\"")) {
                int startIndex = jsonResponse.indexOf("\"content\":\"") + 11;
                int endIndex = jsonResponse.indexOf("\"", startIndex);
                if (endIndex > startIndex) {
                    String content = jsonResponse.substring(startIndex, endIndex);
                    return content.replace("\\n", "\n")
                            .replace("\\\"", "\"")
                            .replace("\\\\", "\\");
                }
            }
            return "无法解析响应内容";
        } catch (Exception e) {
            return "解析响应失败: " + e.getMessage();
        }
    }

 


}
