import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Random;

public class LoginFrame extends JFrame {
    private JTextField emailField;
    private JTextField codeField;
    private JButton sendCodeButton;
    private JButton loginButton;
    private String generatedCode;

    public LoginFrame() {
        // 设置窗口基本属性
        setTitle("AI聊天 - 登录");
        setSize(400, 300);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setLayout(new BorderLayout());

        // 创建主面板
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // 创建标题
        JLabel titleLabel = new JLabel("AI聊天助手登录");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 18));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        mainPanel.add(titleLabel);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 20)));

        // 创建邮箱输入区域
        JPanel emailPanel = new JPanel(new BorderLayout(10, 0));
        JLabel emailLabel = new JLabel("邮箱:");
        emailField = new JTextField(20);
        emailPanel.add(emailLabel, BorderLayout.WEST);
        emailPanel.add(emailField, BorderLayout.CENTER);
        mainPanel.add(emailPanel);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 10)));

        // 创建验证码区域
        JPanel codePanel = new JPanel(new BorderLayout(10, 0));
        JLabel codeLabel = new JLabel("验证码:");
        codeField = new JTextField(10);
        sendCodeButton = new JButton("发送验证码");
        codePanel.add(codeLabel, BorderLayout.WEST);
        codePanel.add(codeField, BorderLayout.CENTER);
        codePanel.add(sendCodeButton, BorderLayout.EAST);
        mainPanel.add(codePanel);
        mainPanel.add(Box.createRigidArea(new Dimension(0, 20)));

        // 创建登录按钮
        loginButton = new JButton("登录");
        loginButton.setAlignmentX(Component.CENTER_ALIGNMENT);
        mainPanel.add(loginButton);

        // 添加到主窗口
        add(mainPanel, BorderLayout.CENTER);

        // 添加事件监听器
        sendCodeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                sendVerificationCode();
            }
        });

        loginButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                verifyAndLogin();
            }
        });
    }

    // 生成并发送验证码
    private void sendVerificationCode() {
        String email = emailField.getText().trim();
        if (email.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入邮箱地址", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // 验证邮箱格式
        if (!isValidEmail(email)) {
            JOptionPane.showMessageDialog(this, "请输入有效的邮箱地址", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // 生成4位随机验证码
        generatedCode = generateRandomCode();

        // 禁用发送按钮，防止重复点击
        sendCodeButton.setEnabled(false);
        sendCodeButton.setText("发送中...");

        // 异步发送邮件
        new Thread(() -> {
            try {
                boolean success = EmailSender.sendVerificationCode(email, generatedCode);

                SwingUtilities.invokeLater(() -> {
                    if (success) {
                        JOptionPane.showMessageDialog(this,
                                "验证码已发送到邮箱: " + email + "\n请查收邮件并输入验证码",
                                "验证码已发送", JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        JOptionPane.showMessageDialog(this,
                                "验证码发送失败，请检查邮箱地址或网络连接\n" +
                                "临时验证码（仅用于测试）: " + generatedCode,
                                "发送失败", JOptionPane.ERROR_MESSAGE);
                    }

                    // 重新启用发送按钮
                    sendCodeButton.setEnabled(true);
                    sendCodeButton.setText("发送验证码");
                });

            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(this,
                            "发送验证码时出现错误: " + e.getMessage() + "\n" +
                            "临时验证码（仅用于测试）: " + generatedCode,
                            "错误", JOptionPane.ERROR_MESSAGE);

                    // 重新启用发送按钮
                    sendCodeButton.setEnabled(true);
                    sendCodeButton.setText("发送验证码");
                });
            }
        }).start();
    }

    // 验证并登录
    private void verifyAndLogin() {
        String email = emailField.getText().trim();
        String code = codeField.getText().trim();

        if (email.isEmpty() || code.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入邮箱和验证码", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (generatedCode == null) {
            JOptionPane.showMessageDialog(this, "请先获取验证码", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (code.equals(generatedCode)) {
            JOptionPane.showMessageDialog(this, "登录成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
            // 登录成功，打开主界面
            openMainFrame();
        } else {
            JOptionPane.showMessageDialog(this, "验证码错误，请重试", "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    // 生成4位随机验证码
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            code.append(random.nextInt(10)); // 0-9的随机数
        }
        return code.toString();
    }

    // 验证邮箱格式
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        // 简单的邮箱格式验证
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }

    // 打开主界面
    private void openMainFrame() {
        this.dispose(); // 关闭登录窗口
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                Main.createAndShowMainFrame(); // 直接显示聊天界面
            }
        });
    }
}